# 数据可视化代码详细解析

## 📋 函数列表与作用说明

### 🔧 核心配置函数

#### `setup_chinese_font_final()`
**作用**: 配置中文字体支持，解决中文显示问题
**关键技术**:
- 直接指定字体文件路径 (`FontProperties`)
- 多重备选字体配置
- 字体缓存刷新

```python
def setup_chinese_font_final():
    # 方案1: 直接字体文件路径（最可靠）
    font_paths = ['C:/Windows/Fonts/msyh.ttc', 'C:/Windows/Fonts/simhei.ttf']
    
    # 方案2: 系统字体配置
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'SimSun']
    plt.rcParams['axes.unicode_minus'] = False
```

#### `load_data()`
**作用**: 加载Excel数据文件，包含错误处理
**技术要点**:
- 使用pandas读取Excel文件
- 异常处理确保程序稳定性
- 返回三个数据集的元组

### 🏠 二手房数据可视化函数

#### `chart1_house_scatter(house_df)`
**图表类型**: 散点图 + 趋势线
**分析目的**: 展示房屋面积与总价的关系
**技术实现**:
```python
# 1. 按区域分组绘制散点
for i, district in enumerate(house_df['所在区'].unique()):
    district_data = house_df[house_df['所在区'] == district]
    plt.scatter(district_data['面积（平方米）'], district_data['总价（万元）'])

# 2. 添加趋势线
z = np.polyfit(house_df['面积（平方米）'], house_df['总价（万元）'], 1)
p = np.poly1d(z)
plt.plot(house_df['面积（平方米）'], p(house_df['面积（平方米）']), "r--")
```

**设计要点**:
- 使用不同颜色区分各区域
- 添加线性回归趋势线
- 透明度设置增强视觉效果

#### `chart2_house_boxplot(house_df)`
**图表类型**: 箱形图
**分析目的**: 比较各区域房价分布特征
**技术实现**:
```python
# 1. 准备箱形图数据
districts = house_df['所在区'].unique()
data_for_box = [house_df[house_df['所在区'] == district]['单价（元/平方米）'] 
                for district in districts]

# 2. 创建箱形图并设置颜色
box_plot = plt.boxplot(data_for_box, labels=districts, patch_artist=True)
for patch, color in zip(box_plot['boxes'], colors):
    patch.set_facecolor(color)
```

**设计要点**:
- 显示中位数、四分位数和异常值
- 彩色填充增强视觉区分
- 旋转标签避免重叠

#### `chart3_house_bar(house_df)`
**图表类型**: 条形图 + 数值标注
**分析目的**: 对比各区域平均房价
**技术实现**:
```python
# 1. 计算平均价格并排序
avg_price = house_df.groupby('所在区')['单价（元/平方米）'].mean().sort_values(ascending=False)

# 2. 添加数值标签
for bar in bars:
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + 1000,
            f'{int(height):,}', ha='center', va='bottom')
```

**设计要点**:
- 按价格高低排序
- 添加千分位分隔符的数值标签
- 网格线辅助读数

### 🍽️ 餐厅消费数据可视化函数

#### `chart4_restaurant_violin(restaurant_df)`
**图表类型**: 小提琴图
**分析目的**: 展示各分店消费金额的分布密度
**技术实现**:
```python
# 1. 准备小提琴图数据
stores = restaurant_df['分店'].unique()
data_for_violin = [restaurant_df[restaurant_df['分店'] == store]['消费金额（元）'] 
                   for store in stores]

# 2. 创建小提琴图
parts = plt.violinplot(data_for_violin, positions=range(1, len(stores) + 1),
                      showmeans=True, showmedians=True)
```

**设计要点**:
- 同时显示均值和中位数线
- 密度曲线显示数据分布形状
- 颜色填充区分不同分店

#### `chart5_restaurant_bubble(restaurant_df)`
**图表类型**: 气泡图 + 性别分组
**分析目的**: 分析消费金额与满意度的关系
**技术实现**:
```python
# 按性别分组绘制
for gender, color in zip(['男', '女'], ['#4ECDC4', '#FF6B6B']):
    gender_data = restaurant_df[restaurant_df['性别'] == gender]
    plt.scatter(gender_data['消费金额（元）'], gender_data['顾客满意度'],
               s=60, alpha=0.6, color=color, label=gender)
```

**设计要点**:
- 性别用不同颜色区分
- 透明度处理重叠点
- 白色边框增强视觉效果

#### `chart6_restaurant_donut(restaurant_df)`
**图表类型**: 环形图
**分析目的**: 展示顾客类型构成比例
**技术实现**:
```python
# 1. 创建饼图
plt.pie(customer_type_counts.values, labels=customer_type_counts.index,
        colors=colors, autopct='%1.1f%%', startangle=90, pctdistance=0.85)

# 2. 添加中心圆形成环形效果
centre_circle = plt.Circle((0,0), 0.70, fc='white')
fig.gca().add_artist(centre_circle)

# 3. 中心添加统计信息
plt.text(0, 0, f'总顾客数\n{len(restaurant_df)}', ha='center', va='center')
```

**设计要点**:
- 环形设计比传统饼图更美观
- 中心显示总数信息
- 百分比标签精确到小数点后一位

### 📈 GDP数据可视化函数

#### `chart7_gdp_line(gdp_df)`
**图表类型**: 多系列折线图
**分析目的**: 展示GDP及各产业的时间序列趋势
**技术实现**:
```python
# 1. 数据透视转换
gdp_melted = pd.melt(gdp_df, id_vars=['指标'], var_name='季度', value_name='数值')
gdp_pivot = gdp_melted.pivot(index='季度', columns='指标', values='数值')

# 2. 绘制多条折线
for i, column in enumerate(gdp_pivot.columns):
    plt.plot(gdp_pivot.index, gdp_pivot[column], 
            marker='o', linewidth=3, markersize=6, 
            color=colors[i], label=column, alpha=0.8)
```

**设计要点**:
- 数据预处理：宽格式转长格式
- 多系列用不同颜色和标记
- 粗线条和大标记点增强可读性

#### `chart8_gdp_area(gdp_df)`
**图表类型**: 堆叠面积图
**分析目的**: 展示三大产业结构变化
**技术实现**:
```python
# 1. 计算各产业占比
industry_data = gdp_pivot[industry_cols]
industry_pct = industry_data.div(industry_data.sum(axis=1), axis=0) * 100

# 2. 创建堆叠面积图
plt.stackplot(industry_pct.index, 
             industry_pct.iloc[:, 0], industry_pct.iloc[:, 1], industry_pct.iloc[:, 2],
             labels=['第一产业', '第二产业', '第三产业'],
             colors=['#96CEB4', '#4ECDC4', '#FF6B6B'], alpha=0.8)
```

**设计要点**:
- 百分比堆叠显示相对占比
- 渐变色彩增强层次感
- 图例清晰标识各产业

#### `chart9_gdp_radar(gdp_df)`
**图表类型**: 多系列雷达图
**分析目的**: 多维度对比各季度产业表现
**技术实现**:
```python
# 1. 设置雷达图角度
angles = np.linspace(0, 2 * np.pi, len(industry_cols), endpoint=False).tolist()
angles += angles[:1]  # 闭合图形

# 2. 创建极坐标子图
fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))

# 3. 绘制多个季度数据
for i, (quarter, row) in enumerate(recent_data.iterrows()):
    values = row.tolist()
    values += values[:1]  # 闭合数据
    ax.plot(angles, values, 'o-', linewidth=2, label=quarter, color=colors[i])
    ax.fill(angles, values, alpha=0.25, color=colors[i])
```

**设计要点**:
- 极坐标系统适合多维对比
- 填充区域增强视觉效果
- 闭合图形确保完整性

## 🎨 通用设计函数

#### `create_chart_with_chinese()`
**作用**: 统一的中文图表创建包装器
**功能**:
- 统一字体设置
- 错误处理
- 文件保存
- 进度反馈

```python
def create_chart_with_chinese(fig_func, title, filename, *args, **kwargs):
    try:
        fig_func(*args, **kwargs)
        plt.title(title, fontsize=16, fontweight='bold', pad=20)
        if chinese_font:
            plt.title(title, fontproperties=chinese_font, fontsize=16, fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(f'charts_final/{filename}', dpi=300, bbox_inches='tight')
        plt.show()
        print(f"✅ {filename} 创建成功")
    except Exception as e:
        print(f"❌ {filename} 创建失败: {e}")
```

## 🔍 关键技术要点

### 1. 数据预处理技术
- **透视表转换**: `pd.melt()` 和 `pivot()` 实现数据格式转换
- **分组聚合**: `groupby()` 计算统计指标
- **数据筛选**: 布尔索引实现条件筛选

### 2. 可视化技术
- **多系列绘图**: 循环绘制不同类别的数据
- **颜色管理**: 统一的配色方案确保视觉一致性
- **标注技术**: 自动添加数值标签和说明文字

### 3. 布局优化
- **图表尺寸**: 根据内容调整合适的figsize
- **边距控制**: `tight_layout()` 自动优化布局
- **高分辨率**: 300 DPI确保输出质量

### 4. 错误处理
- **异常捕获**: try-except确保程序稳定性
- **数据验证**: 检查数据完整性
- **用户反馈**: 清晰的成功/失败提示

## 📊 图表分析方法

### 相关性分析
```python
# 计算相关系数
correlation = np.corrcoef(x_data, y_data)[0, 1]
print(f"相关系数: {correlation:.2f}")
```

### 趋势分析
```python
# 线性回归趋势线
z = np.polyfit(x_data, y_data, 1)
trend_line = np.poly1d(z)
```

### 分布分析
```python
# 统计描述
mean_value = data.mean()
median_value = data.median()
std_value = data.std()
```

## 🎯 最佳实践总结

1. **模块化设计**: 每个图表独立函数，便于维护
2. **统一风格**: 一致的配色、字体、布局
3. **错误处理**: 完善的异常处理机制
4. **文档注释**: 清晰的函数说明和注释
5. **可复用性**: 通用函数减少代码重复

这份代码解析文档详细说明了每个图表的实现原理和技术要点，为理解和学习数据可视化技术提供了完整的参考。
